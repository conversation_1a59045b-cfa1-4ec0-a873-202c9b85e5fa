{"python.formatting.provider": "black", "eslint.workingDirectories": [{"mode": "auto"}], "eslint.validate": ["javascript"], "notebook.formatOnSave.enabled": true, "notebook.formatOnCellExecution": true, "python.analysis.extraPaths": ["./api"], "python.languageServer": "None", "python.analysis.typeCheckingMode": "standard", "typescript.tsdk": "node_modules/typescript/lib", "cursorpyright.analysis.extraPaths": ["./api"], "cursorpyright.analysis.typeCheckingMode": "standard"}